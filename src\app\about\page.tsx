import { Metadata } from 'next'
import { Card, CardContent } from '@/components/ui/card'
import { Music, Heart, Users, Award, Target, BookOpen } from 'lucide-react'

export const metadata: Metadata = {
  title: 'About Us - Sangam Music Academy | Premier Music Education in Biratnagar',
  description: 'Learn about Sangam Music Academy\'s 9+ years of excellence in music education. Discover our teaching philosophy, experienced instructors, and comprehensive music programs in Biratnagar, Nepal.',
  keywords: 'about music academy, music education Biratnagar, music school Nepal, guitar piano vocal tabla harmonium flute classes',
}

const instruments = [
  { name: 'Guitar', description: 'Acoustic and electric guitar lessons for all levels' },
  { name: 'Piano', description: 'Classical and contemporary piano instruction' },
  { name: 'Vocal Training', description: 'Voice development and singing techniques' },
  { name: 'Tabla', description: 'Traditional Indian percussion and rhythms' },
  { name: 'Harmonium', description: 'Classical and devotional music on harmonium' },
  { name: 'Flute', description: 'Bamboo flute and classical wind instruments' }
]

const values = [
  {
    icon: Heart,
    title: 'Passion for Music',
    description: 'We believe music is a universal language that connects hearts and souls.'
  },
  {
    icon: Users,
    title: 'Community Focus',
    description: 'Building a supportive community of musicians and music lovers.'
  },
  {
    icon: Award,
    title: 'Excellence',
    description: 'Committed to providing the highest quality music education.'
  },
  {
    icon: Target,
    title: 'Individual Growth',
    description: 'Nurturing each student\'s unique musical journey and potential.'
  }
]

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: "url('https://images.unsplash.com/photo-1507838153414-b4b713384a76?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80')"
          }}
        ></div>
        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/90 to-blue-800/90"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">About Sangam Music Academy</h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Nurturing musical talent and inspiring creativity in the heart of Biratnagar since 2015
            </p>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Our Story</h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  Founded in 2015 by a group of passionate musicians, Sangam Music Academy began as a small 
                  initiative to bring quality music education to Biratnagar. What started with just a handful 
                  of students has grown into one of the region&apos;s most respected music institutions.
                </p>
                <p>
                  Our academy was born from the belief that music education should be accessible to everyone,
                  regardless of age or background. We&apos;ve created a nurturing environment where students can
                  explore their musical interests, develop their skills, and build lasting friendships through
                  the universal language of music.
                </p>
                <p>
                  Today, we&apos;re proud to have taught over 500 students and continue to be a cornerstone of
                  musical education in our community. Our graduates have gone on to pursue professional
                  music careers, while others simply enjoy music as a lifelong passion.
                </p>
              </div>
            </div>
            <div className="relative rounded-lg overflow-hidden">
              <div
                className="h-96 bg-cover bg-center"
                style={{
                  backgroundImage: "url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')"
                }}
              ></div>
              <div className="absolute inset-0 bg-blue-900/80 flex items-center justify-center">
                <div className="text-center text-white p-8">
                  <Music className="h-16 w-16 mx-auto mb-4 text-yellow-400" />
                  <h3 className="text-2xl font-bold mb-4">9+ Years of Excellence</h3>
                  <p className="text-blue-100">
                    Dedicated to providing exceptional music education and fostering a love for music
                    in students of all ages.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Teaching Philosophy */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Our Teaching Philosophy</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We believe that every student has unique musical potential waiting to be discovered and nurtured.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center border-0 shadow-lg card-hover">
                <CardContent className="p-8">
                  <div className="flex justify-center mb-4">
                    <div className="p-3 bg-blue-100 rounded-full">
                      <value.icon className="h-8 w-8 text-blue-900" />
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{value.title}</h3>
                  <p className="text-gray-600">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Instruments We Teach */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Instruments We Teach</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From traditional to contemporary, we offer comprehensive instruction across a wide range of instruments.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {instruments.map((instrument, index) => (
              <Card key={index} className="border-0 shadow-lg card-hover">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="p-2 bg-blue-100 rounded-lg mr-4">
                      <BookOpen className="h-6 w-6 text-blue-900" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">{instrument.name}</h3>
                  </div>
                  <p className="text-gray-600">{instrument.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-blue-900 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Start Your Musical Journey?</h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join our community of music lovers and discover the joy of learning music in a supportive environment.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="/contact" 
              className="inline-flex items-center px-8 py-4 bg-yellow-500 hover:bg-yellow-600 text-blue-900 font-semibold rounded-lg transition-colors"
            >
              Schedule a Trial Class
            </a>
            <a 
              href="/courses" 
              className="inline-flex items-center px-8 py-4 border-2 border-white text-white hover:bg-white hover:text-blue-900 font-semibold rounded-lg transition-colors"
            >
              View Our Courses
            </a>
          </div>
        </div>
      </section>
    </div>
  )
}
