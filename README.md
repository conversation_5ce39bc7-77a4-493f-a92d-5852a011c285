# Sangam Music Academy Website

A modern, responsive website for a music academy built with Next.js, ShadCN UI, and TanStack Query. This website showcases music courses, instructors, pricing, and provides a complete digital presence for a music education institution.

## 🎵 Features

- **Modern Design**: Clean, professional design with a music-focused aesthetic
- **Responsive Layout**: Fully responsive design that works on all devices
- **Course Management**: Dynamic course listings with filtering capabilities
- **Instructor Profiles**: Detailed instructor information and specializations
- **Pricing Plans**: Flexible pricing options with feature comparisons
- **Contact Forms**: Interactive contact forms with validation
- **Gallery**: Photo and video gallery showcasing academy activities
- **SEO Optimized**: Complete SEO implementation with meta tags and structured data
- **Performance**: Optimized for speed and performance

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS + ShadCN UI Components
- **Data Fetching**: TanStack Query (React Query)
- **TypeScript**: Full TypeScript support
- **Icons**: Lucide React
- **Fonts**: Inter font family

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd music-academy
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── about/             # About page
│   ├── contact/           # Contact page
│   ├── courses/           # Courses page
│   ├── gallery/           # Gallery page
│   ├── instructors/       # Instructors page
│   ├── pricing/           # Pricing page
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Homepage
│   ├── not-found.tsx      # 404 page
│   ├── robots.ts          # Robots.txt
│   └── sitemap.ts         # Sitemap
├── components/
│   ├── layout/            # Layout components (Header, Footer)
│   ├── sections/          # Page sections (Hero, Features, etc.)
│   ├── seo/              # SEO components
│   └── ui/               # ShadCN UI components
├── lib/
│   ├── mock-data.ts      # Mock data for courses, instructors, etc.
│   └── utils.ts          # Utility functions
└── providers/
    └── query-provider.tsx # TanStack Query provider
```

## 🎨 Customization

### Colors and Branding

The website uses a blue and gold color scheme suitable for a music academy. To customize:

1. **Primary Colors**: Edit the CSS variables in `src/app/globals.css`
2. **Logo**: Replace the Music icon in the header component
3. **Content**: Update mock data in `src/lib/mock-data.ts`

### Adding Real Data

Currently, the website uses mock data. To integrate with a real backend:

1. Replace mock API functions in `src/lib/mock-data.ts`
2. Update TanStack Query calls to use real API endpoints
3. Add authentication if needed

### Contact Form

The contact form currently shows an alert on submission. To integrate with a real backend:

1. Update the `handleSubmit` function in `src/app/contact/page.tsx`
2. Add form validation library (e.g., React Hook Form + Zod)
3. Integrate with email service or backend API

## 🌐 SEO Features

- **Meta Tags**: Comprehensive meta tags for all pages
- **Structured Data**: JSON-LD structured data for better search visibility
- **Sitemap**: Automatically generated sitemap
- **Robots.txt**: Search engine crawling instructions
- **Open Graph**: Social media sharing optimization

## 📱 Mobile Optimization

- Responsive design for all screen sizes
- Touch-friendly navigation
- Optimized images and performance
- Mobile-first CSS approach

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms

The website can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Railway
- DigitalOcean App Platform

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support or questions about this project, please open an issue in the repository.
