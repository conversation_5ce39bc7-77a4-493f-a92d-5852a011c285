'use client'

import { useQuery } from '@tanstack/react-query'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { fetchInstructors } from '@/lib/mock-data'
import { User, Award, Music } from 'lucide-react'

export default function InstructorsPage() {
  const { data: instructors, isLoading, error } = useQuery({
    queryKey: ['instructors'],
    queryFn: fetchInstructors,
  })

  if (isLoading) {
    return (
      <div className="min-h-screen">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-blue-900 to-blue-800 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Instructors</h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Meet our talented team of music educators
            </p>
          </div>
        </section>

        {/* Loading State */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-200 h-96 rounded-lg"></div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Instructors</h2>
          <p className="text-gray-600">Please try again later.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Instructors</h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Meet our passionate and experienced team of music educators who are dedicated to 
            helping you achieve your musical goals. Each instructor brings years of performance 
            and teaching experience to guide you on your musical journey.
          </p>
        </div>
      </section>

      {/* Instructors Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Expert Musicians & Educators
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our instructors are not just teachers, but accomplished musicians who bring real-world 
              experience and passion to every lesson.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {instructors?.map((instructor) => (
              <Card key={instructor.id} className="card-hover border-0 shadow-lg overflow-hidden">
                <div className="relative">
                  {/* Instructor image */}
                  <div
                    className="h-64 bg-cover bg-center bg-no-repeat"
                    style={{
                      backgroundImage: `url('${instructor.image}')`
                    }}
                  ></div>
                  
                  {/* Experience Badge */}
                  <div className="absolute top-4 right-4">
                    <Badge className="bg-yellow-400 text-blue-900 font-semibold">
                      {instructor.experience}
                    </Badge>
                  </div>
                </div>

                <CardContent className="p-6">
                  {/* Name and Primary Instrument */}
                  <div className="text-center mb-4">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">
                      {instructor.name}
                    </h3>
                    <p className="text-blue-900 font-semibold text-lg">
                      {instructor.instrument}
                    </p>
                  </div>

                  {/* Bio */}
                  <p className="text-gray-600 text-sm leading-relaxed mb-4">
                    {instructor.bio}
                  </p>

                  {/* Specializations */}
                  <div className="mb-4">
                    <div className="flex items-center mb-2">
                      <Music className="h-4 w-4 text-blue-900 mr-2" />
                      <span className="text-sm font-medium text-gray-900">Specializations:</span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {instructor.specialization.map((spec, index) => (
                        <Badge 
                          key={index} 
                          variant="outline" 
                          className="text-xs border-blue-200 text-blue-800"
                        >
                          {spec}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Experience */}
                  <div className="flex items-center text-sm text-gray-600">
                    <Award className="h-4 w-4 text-blue-900 mr-2" />
                    <span>{instructor.experience} of teaching experience</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Why Our Instructors Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Why Our Instructors Stand Out
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="flex justify-center mb-4">
                <div className="p-3 bg-blue-100 rounded-full">
                  <Award className="h-8 w-8 text-blue-900" />
                </div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Professional Experience</h3>
              <p className="text-gray-600">
                All our instructors are accomplished musicians with years of performance 
                and teaching experience in their respective instruments.
              </p>
            </div>

            <div className="text-center">
              <div className="flex justify-center mb-4">
                <div className="p-3 bg-blue-100 rounded-full">
                  <User className="h-8 w-8 text-blue-900" />
                </div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Personalized Approach</h3>
              <p className="text-gray-600">
                Each instructor adapts their teaching style to match individual student needs, 
                ensuring effective and enjoyable learning experiences.
              </p>
            </div>

            <div className="text-center">
              <div className="flex justify-center mb-4">
                <div className="p-3 bg-blue-100 rounded-full">
                  <Music className="h-8 w-8 text-blue-900" />
                </div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Continuous Learning</h3>
              <p className="text-gray-600">
                Our instructors regularly update their skills and teaching methods to provide 
                the most current and effective music education.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-blue-900 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Learn from the Best?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Schedule a trial lesson with one of our expert instructors and experience 
            the difference quality teaching makes.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="/contact" 
              className="inline-flex items-center px-8 py-4 bg-yellow-500 hover:bg-yellow-600 text-blue-900 font-semibold rounded-lg transition-colors"
            >
              Schedule Trial Lesson
            </a>
            <a 
              href="/courses" 
              className="inline-flex items-center px-8 py-4 border-2 border-white text-white hover:bg-white hover:text-blue-900 font-semibold rounded-lg transition-colors"
            >
              View Our Courses
            </a>
          </div>
        </div>
      </section>
    </div>
  )
}
