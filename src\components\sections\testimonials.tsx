'use client'

import { useQuery } from '@tanstack/react-query'
import { Card, CardContent } from '@/components/ui/card'
import { fetchTestimonials } from '@/lib/mock-data'
import { Star, Quote } from 'lucide-react'

interface TestimonialsProps {
  showTitle?: boolean
  limit?: number
  className?: string
}

export function TestimonialsSection({ showTitle = true, limit, className = "" }: TestimonialsProps) {
  const { data: testimonials, isLoading } = useQuery({
    queryKey: ['testimonials'],
    queryFn: fetchTestimonials,
  })

  if (isLoading) {
    return (
      <section className={`py-20 bg-blue-50 ${className}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {showTitle && (
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                What Our Students Say
              </h2>
            </div>
          )}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 h-64 rounded-lg"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  const displayTestimonials = limit ? testimonials?.slice(0, limit) : testimonials

  return (
    <section className={`py-20 bg-blue-50 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {showTitle && (
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What Our Students Say
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Don&apos;t just take our word for it. Here&apos;s what our students and their families 
              have to say about their experience at Sangam Music Academy.
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {displayTestimonials?.map((testimonial) => (
            <Card key={testimonial.id} className="card-hover border-0 shadow-lg bg-white">
              <CardContent className="p-8">
                {/* Quote Icon */}
                <div className="flex justify-center mb-4">
                  <Quote className="h-8 w-8 text-blue-900 opacity-20" />
                </div>

                {/* Rating Stars */}
                <div className="flex justify-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${
                        i < testimonial.rating
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>

                {/* Testimonial Message */}
                <blockquote className="text-gray-700 text-center mb-6 leading-relaxed italic">
                  &quot;{testimonial.message}&quot;
                </blockquote>

                {/* Student Info */}
                <div className="text-center">
                  {/* Student photo */}
                  <div className="flex justify-center mb-3">
                    <div
                      className="w-12 h-12 rounded-full bg-cover bg-center border-2 border-blue-200"
                      style={{
                        backgroundImage: `url('${testimonial.image}')`
                      }}
                    ></div>
                  </div>
                  
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {testimonial.name}
                  </h4>
                  <p className="text-sm text-blue-900 font-medium">
                    {testimonial.course}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View More Button (if limited) */}
        {limit && testimonials && testimonials.length > limit && (
          <div className="text-center mt-12">
            <a 
              href="/testimonials" 
              className="inline-flex items-center px-6 py-3 bg-blue-900 hover:bg-blue-800 text-white font-semibold rounded-lg transition-colors"
            >
              Read More Reviews
            </a>
          </div>
        )}
      </div>
    </section>
  )
}
