'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Camera, Play, Users, Award, Filter } from 'lucide-react'

// Mock gallery data
const galleryItems = [
  {
    id: 1,
    title: 'Guitar Class Session',
    category: 'Classes',
    type: 'image',
    image: 'https://images.unsplash.com/photo-1510915361894-db8b60106cb1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    description: 'Students learning basic guitar chords in our comfortable classroom setting.'
  },
  {
    id: 2,
    title: 'Piano Recital 2024',
    category: 'Performances',
    type: 'image',
    image: 'https://images.unsplash.com/photo-1520523839897-bd0b52f945a0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    description: 'Annual piano recital showcasing our students\' progress and talent.'
  },
  {
    id: 3,
    title: 'Vocal Training Workshop',
    category: 'Classes',
    type: 'video',
    image: 'https://images.unsplash.com/photo-1516280440614-37939bbacd81?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    description: 'Intensive vocal training workshop focusing on breathing techniques.'
  },
  {
    id: 4,
    title: 'Tabla Performance',
    category: 'Performances',
    type: 'image',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    description: 'Traditional tabla performance by our advanced students.'
  },
  {
    id: 5,
    title: 'Group Harmonium Session',
    category: 'Classes',
    type: 'image',
    image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    description: 'Students learning devotional music on harmonium in a group setting.'
  },
  {
    id: 6,
    title: 'Student Concert 2024',
    category: 'Performances',
    type: 'video',
    image: 'https://images.unsplash.com/photo-1501386761578-eac5c94b800a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    description: 'Highlights from our annual student concert featuring all instruments.'
  },
  {
    id: 7,
    title: 'Flute Ensemble Practice',
    category: 'Classes',
    type: 'image',
    image: 'https://images.unsplash.com/photo-1465821185615-20b3c2fbf41b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1169&q=80',
    description: 'Flute ensemble practicing traditional Nepali folk melodies.'
  },
  {
    id: 8,
    title: 'Music Theory Class',
    category: 'Classes',
    type: 'image',
    image: 'https://images.unsplash.com/photo-1507838153414-b4b713384a76?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    description: 'Interactive music theory session with visual aids and practical examples.'
  },
  {
    id: 9,
    title: 'Cultural Festival Performance',
    category: 'Performances',
    type: 'video',
    image: 'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    description: 'Our students performing at the local cultural festival.'
  },
  {
    id: 10,
    title: 'Advanced Guitar Workshop',
    category: 'Workshops',
    type: 'image',
    image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    description: 'Special workshop on advanced guitar techniques and improvisation.'
  },
  {
    id: 11,
    title: 'Children\'s Music Class',
    category: 'Classes',
    type: 'image',
    image: 'https://images.unsplash.com/photo-1503095396549-807759245b35?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1171&q=80',
    description: 'Young students enjoying their introduction to music fundamentals.'
  },
  {
    id: 12,
    title: 'Graduation Ceremony',
    category: 'Events',
    type: 'image',
    image: 'https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    description: 'Certificate presentation ceremony for course completion.'
  }
]

const categories = ['All', 'Classes', 'Performances', 'Workshops', 'Events']

export default function GalleryPage() {
  const [selectedCategory, setSelectedCategory] = useState('All')

  const filteredItems = selectedCategory === 'All' 
    ? galleryItems 
    : galleryItems.filter(item => item.category === selectedCategory)

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Gallery</h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Take a glimpse into the vibrant musical life at Sangam Music Academy. 
            From classroom sessions to spectacular performances, see our students in action.
          </p>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-8 bg-gray-50 border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5 text-gray-600" />
              <span className="font-medium text-gray-900">Filter by Category:</span>
            </div>
            
            <div className="flex gap-2 flex-wrap">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className={selectedCategory === category ? "bg-blue-900 hover:bg-blue-800" : ""}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {filteredItems.length} {selectedCategory === 'All' ? 'Items' : selectedCategory}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredItems.map((item) => (
              <Card key={item.id} className="card-hover border-0 shadow-lg overflow-hidden group">
                <div className="relative">
                  {/* Image/video */}
                  <div
                    className="aspect-square bg-cover bg-center relative overflow-hidden"
                    style={{
                      backgroundImage: `url('${item.image}')`
                    }}
                  >
                    {item.type === 'video' && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Play className="h-16 w-16 text-white opacity-80 group-hover:opacity-100 transition-opacity drop-shadow-lg" />
                      </div>
                    )}

                    {/* Overlay on hover */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        {item.type === 'video' ? (
                          <div className="bg-white bg-opacity-90 rounded-full p-3">
                            <Play className="h-8 w-8 text-blue-900" />
                          </div>
                        ) : (
                          <div className="bg-white bg-opacity-90 rounded-full p-3">
                            <Camera className="h-8 w-8 text-blue-900" />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Category Badge */}
                  <div className="absolute top-3 left-3">
                    <Badge className="bg-blue-900 text-white">
                      {item.category}
                    </Badge>
                  </div>

                  {/* Type Badge */}
                  <div className="absolute top-3 right-3">
                    <Badge variant="secondary" className="bg-white text-blue-900">
                      {item.type === 'video' ? 'Video' : 'Photo'}
                    </Badge>
                  </div>
                </div>

                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-1">
                    {item.title}
                  </h3>
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {item.description}
                  </p>
                </div>
              </Card>
            ))}
          </div>

          {filteredItems.length === 0 && (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No items found</h3>
              <p className="text-gray-600">Try selecting a different category.</p>
            </div>
          )}
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Capturing Musical Moments
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Every photo and video tells a story of growth, achievement, and the joy of music.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="flex justify-center mb-4">
                <div className="p-3 bg-blue-100 rounded-full">
                  <Camera className="h-8 w-8 text-blue-900" />
                </div>
              </div>
              <div className="text-3xl font-bold text-blue-900 mb-2">200+</div>
              <div className="text-gray-600">Photos Captured</div>
            </div>

            <div className="text-center">
              <div className="flex justify-center mb-4">
                <div className="p-3 bg-blue-100 rounded-full">
                  <Play className="h-8 w-8 text-blue-900" />
                </div>
              </div>
              <div className="text-3xl font-bold text-blue-900 mb-2">50+</div>
              <div className="text-gray-600">Performance Videos</div>
            </div>

            <div className="text-center">
              <div className="flex justify-center mb-4">
                <div className="p-3 bg-blue-100 rounded-full">
                  <Users className="h-8 w-8 text-blue-900" />
                </div>
              </div>
              <div className="text-3xl font-bold text-blue-900 mb-2">100+</div>
              <div className="text-gray-600">Students Featured</div>
            </div>

            <div className="text-center">
              <div className="flex justify-center mb-4">
                <div className="p-3 bg-blue-100 rounded-full">
                  <Award className="h-8 w-8 text-blue-900" />
                </div>
              </div>
              <div className="text-3xl font-bold text-blue-900 mb-2">25+</div>
              <div className="text-gray-600">Events Documented</div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-blue-900 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Be Part of Our Musical Story
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join our academy and create your own musical memories. Your journey could be featured 
            in our next gallery update!
          </p>
          <Button asChild size="lg" className="bg-yellow-500 hover:bg-yellow-600 text-blue-900">
            <a href="/contact">
              Start Your Musical Journey
            </a>
          </Button>
        </div>
      </section>
    </div>
  )
}
