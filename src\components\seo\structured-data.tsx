export function MusicAcademyStructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "MusicSchool",
    "name": "Sangam Music Academy",
    "description": "Premier music academy in Biratnagar offering guitar, piano, vocal, tabla, harmonium, and flute classes with expert instructors.",
    "url": "https://sangammusic.com",
    "telephone": "+977-9841234567",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Traffic Chowk, Biratnagar-10",
      "addressLocality": "Biratnagar",
      "addressRegion": "Morang",
      "addressCountry": "Nepal"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "26.4589",
      "longitude": "87.2789"
    },
    "openingHours": [
      "Mo-Fr 09:00-19:00",
      "Sa 09:00-17:00",
      "Su 10:00-16:00"
    ],
    "priceRange": "NPR 2,500 - NPR 3,500",
    "image": "https://sangammusic.com/logo.png",
    "sameAs": [
      "https://www.facebook.com/sangammusic",
      "https://www.instagram.com/sangammusic",
      "https://www.youtube.com/sangammusic"
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Music Courses",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Course",
            "name": "Guitar Classes",
            "description": "Learn guitar from beginner to advanced levels"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Course",
            "name": "Piano Classes",
            "description": "Classical and contemporary piano instruction"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Course",
            "name": "Vocal Training",
            "description": "Voice development and singing techniques"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Course",
            "name": "Tabla Classes",
            "description": "Traditional Indian percussion and rhythms"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Course",
            "name": "Harmonium Classes",
            "description": "Classical and devotional music on harmonium"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Course",
            "name": "Flute Classes",
            "description": "Bamboo flute and classical wind instruments"
          }
        }
      ]
    }
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}
