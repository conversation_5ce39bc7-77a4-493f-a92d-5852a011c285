'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Music, Users, Clock, Award, Heart, BookOpen } from 'lucide-react'
import { useStaggeredAnimation } from '@/hooks/use-scroll-animation'

const features = [
  {
    icon: Music,
    title: 'Expert Instructors',
    description: 'Learn from experienced musicians with years of teaching and performance experience.'
  },
  {
    icon: Users,
    title: 'Small Class Sizes',
    description: 'Personalized attention with small groups ensuring focused learning for every student.'
  },
  {
    icon: Clock,
    title: 'Flexible Schedules',
    description: 'Choose from morning, afternoon, or evening classes that fit your busy lifestyle.'
  },
  {
    icon: Award,
    title: 'Proven Methods',
    description: 'Time-tested teaching techniques combined with modern approaches for effective learning.'
  },
  {
    icon: Heart,
    title: 'Nurturing Environment',
    description: 'A supportive and encouraging atmosphere where students feel comfortable to express themselves.'
  },
  {
    icon: BookOpen,
    title: 'Comprehensive Curriculum',
    description: 'Structured lessons covering theory, technique, and practical application for complete musical education.'
  }
]

export function FeaturesSection() {
  const { ref, visibleItems, isVisible } = useStaggeredAnimation(features.length, 150)

  return (
    <section ref={ref} className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className={`text-center mb-16 transition-all duration-800 ${
          isVisible ? 'animate-fade-in-up' : 'scroll-animate'
        }`}>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Why Choose Sangam Music Academy?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We provide a comprehensive music education experience that nurtures talent,
            builds confidence, and creates lifelong musicians.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card
              key={index}
              className={`card-hover-enhanced border-0 shadow-lg transition-all duration-700 ${
                visibleItems.includes(index)
                  ? 'animate-fade-in-up'
                  : 'scroll-animate'
              }`}
              style={{
                animationDelay: visibleItems.includes(index) ? `${index * 150}ms` : '0ms'
              }}
            >
              <CardContent className="p-8 text-center">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-blue-100 rounded-full transition-all duration-300 hover:bg-blue-200 hover:scale-110">
                    <feature.icon className="h-8 w-8 text-blue-900 icon-bounce" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3 text-reveal">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
