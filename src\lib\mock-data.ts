export interface Course {
  id: string
  name: string
  instrument: string
  ageGroup: string
  duration: string
  schedule: string
  fee: string
  description: string
  level: 'Beginner' | 'Intermediate' | 'Advanced'
  image: string
}

export interface Instructor {
  id: string
  name: string
  instrument: string
  experience: string
  image: string
  bio: string
  specialization: string[]
}

export interface Testimonial {
  id: string
  name: string
  image: string
  message: string
  rating: number
  course: string
}

export interface PricingPlan {
  id: string
  name: string
  duration: string
  price: string
  features: string[]
  popular?: boolean
}

// Mock Courses Data
export const mockCourses: Course[] = [
  {
    id: '1',
    name: 'Guitar Fundamentals',
    instrument: 'Guitar',
    ageGroup: '8+ years',
    duration: '45 minutes',
    schedule: 'Mon, Wed, Fri - 4:00 PM',
    fee: 'NPR 3,000/month',
    description: 'Learn basic chords, strumming patterns, and popular songs',
    level: 'Beginner',
    image: 'https://images.unsplash.com/photo-1510915361894-db8b60106cb1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80'
  },
  {
    id: '2',
    name: 'Piano Basics',
    instrument: 'Piano',
    ageGroup: '6+ years',
    duration: '45 minutes',
    schedule: 'Tue, Thu, Sat - 3:00 PM',
    fee: 'NPR 3,500/month',
    description: 'Master scales, basic pieces, and music theory fundamentals',
    level: 'Beginner',
    image: 'https://images.unsplash.com/photo-1520523839897-bd0b52f945a0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80'
  },
  {
    id: '3',
    name: 'Vocal Training',
    instrument: 'Voice',
    ageGroup: '10+ years',
    duration: '60 minutes',
    schedule: 'Mon, Wed - 5:00 PM',
    fee: 'NPR 2,800/month',
    description: 'Develop proper breathing, pitch control, and vocal techniques',
    level: 'Beginner',
    image: 'https://images.unsplash.com/photo-1516280440614-37939bbacd81?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80'
  },
  {
    id: '4',
    name: 'Tabla Mastery',
    instrument: 'Tabla',
    ageGroup: '12+ years',
    duration: '60 minutes',
    schedule: 'Tue, Thu - 6:00 PM',
    fee: 'NPR 3,200/month',
    description: 'Traditional tabla rhythms and classical compositions',
    level: 'Intermediate',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80'
  },
  {
    id: '5',
    name: 'Harmonium Classes',
    instrument: 'Harmonium',
    ageGroup: '8+ years',
    duration: '45 minutes',
    schedule: 'Sat, Sun - 2:00 PM',
    fee: 'NPR 2,500/month',
    description: 'Learn devotional and classical music on harmonium',
    level: 'Beginner',
    image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'
  },
  {
    id: '6',
    name: 'Flute Ensemble',
    instrument: 'Flute',
    ageGroup: '10+ years',
    duration: '45 minutes',
    schedule: 'Fri, Sun - 4:00 PM',
    fee: 'NPR 2,800/month',
    description: 'Classical and folk melodies on bamboo flute',
    level: 'Beginner',
    image: 'https://images.unsplash.com/photo-1465821185615-20b3c2fbf41b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1169&q=80'
  }
]

// Mock Instructors Data
export const mockInstructors: Instructor[] = [
  {
    id: '1',
    name: 'Rajesh Sharma',
    instrument: 'Guitar & Piano',
    experience: '12 years',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80',
    bio: 'Classically trained musician with expertise in both Western and Eastern music traditions.',
    specialization: ['Classical Guitar', 'Jazz Piano', 'Music Theory']
  },
  {
    id: '2',
    name: 'Priya Thapa',
    instrument: 'Vocal & Harmonium',
    experience: '8 years',
    image: 'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80',
    bio: 'Renowned vocalist specializing in classical and devotional music.',
    specialization: ['Classical Vocals', 'Bhajans', 'Harmonium']
  },
  {
    id: '3',
    name: 'Bikash Rai',
    instrument: 'Tabla & Percussion',
    experience: '15 years',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    bio: 'Master tabla player trained in the Benares gharana tradition.',
    specialization: ['Classical Tabla', 'Folk Rhythms', 'Percussion Ensemble']
  },
  {
    id: '4',
    name: 'Sunita Gurung',
    instrument: 'Flute',
    experience: '10 years',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    bio: 'Expert in bamboo flute with deep knowledge of Nepali folk music.',
    specialization: ['Bamboo Flute', 'Folk Music', 'Meditation Music']
  }
]

// Mock Testimonials Data
export const mockTestimonials: Testimonial[] = [
  {
    id: '1',
    name: 'Anita Shrestha',
    image: 'https://images.unsplash.com/photo-**********-94ddf0286df2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=688&q=80',
    message: 'My daughter has been learning piano here for 6 months. The teachers are patient and the progress is remarkable!',
    rating: 5,
    course: 'Piano Basics'
  },
  {
    id: '2',
    name: 'Ramesh Poudel',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80',
    message: 'Excellent guitar classes! I went from complete beginner to playing my favorite songs in just 3 months.',
    rating: 5,
    course: 'Guitar Fundamentals'
  },
  {
    id: '3',
    name: 'Kamala Devi',
    image: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80',
    message: 'The vocal training has improved my confidence tremendously. Highly recommend this academy!',
    rating: 5,
    course: 'Vocal Training'
  },
  {
    id: '4',
    name: 'Suresh Tamang',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    message: 'Learning tabla here has connected me with our rich musical heritage. Great teachers and atmosphere.',
    rating: 5,
    course: 'Tabla Mastery'
  },
  {
    id: '5',
    name: 'Maya Karki',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    message: 'The harmonium classes are perfect for devotional music. Very satisfied with the teaching quality.',
    rating: 5,
    course: 'Harmonium Classes'
  }
]

// Mock Pricing Plans
export const mockPricingPlans: PricingPlan[] = [
  {
    id: '1',
    name: 'Monthly Plan',
    duration: '1 Month',
    price: 'NPR 3,000',
    features: [
      '8 classes per month',
      'Individual attention',
      'Practice materials included',
      'Progress tracking'
    ]
  },
  {
    id: '2',
    name: 'Quarterly Plan',
    duration: '3 Months',
    price: 'NPR 8,000',
    features: [
      '24 classes (3 months)',
      'Individual attention',
      'Practice materials included',
      'Progress tracking',
      'Free makeup classes',
      '10% discount'
    ],
    popular: true
  },
  {
    id: '3',
    name: 'Yearly Plan',
    duration: '12 Months',
    price: 'NPR 30,000',
    features: [
      '96 classes (12 months)',
      'Individual attention',
      'Practice materials included',
      'Progress tracking',
      'Free makeup classes',
      'Performance opportunities',
      '20% discount',
      'Certificate upon completion'
    ]
  }
]

// Mock API functions
export const fetchCourses = async (): Promise<Course[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000))
  return mockCourses
}

export const fetchInstructors = async (): Promise<Instructor[]> => {
  await new Promise(resolve => setTimeout(resolve, 800))
  return mockInstructors
}

export const fetchTestimonials = async (): Promise<Testimonial[]> => {
  await new Promise(resolve => setTimeout(resolve, 600))
  return mockTestimonials
}

export const fetchPricingPlans = async (): Promise<PricingPlan[]> => {
  await new Promise(resolve => setTimeout(resolve, 500))
  return mockPricingPlans
}
