'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Music, Home, ArrowLeft } from 'lucide-react'

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-blue-100">
      <div className="max-w-md mx-auto text-center px-4">
        {/* Musical Note Animation */}
        <div className="flex justify-center mb-8">
          <div className="relative">
            <Music className="h-24 w-24 text-blue-900 music-note-animation" />
            <div className="absolute -top-2 -right-2">
              <div className="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                <span className="text-blue-900 font-bold text-lg">?</span>
              </div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        <h1 className="text-6xl font-bold text-blue-900 mb-4">404</h1>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Page Not Found</h2>
        <p className="text-gray-600 mb-8 leading-relaxed">
          Oops! It looks like this page has gone off-key. The page you&apos;re looking for
          doesn&apos;t exist or may have been moved to a different location.
        </p>

        {/* Action Buttons */}
        <div className="space-y-4">
          <Button asChild size="lg" className="w-full bg-blue-900 hover:bg-blue-800">
            <Link href="/">
              <Home className="mr-2 h-5 w-5" />
              Go to Homepage
            </Link>
          </Button>
          
          <Button asChild variant="outline" size="lg" className="w-full">
            <Link href="/courses">
              <Music className="mr-2 h-5 w-5" />
              Browse Courses
            </Link>
          </Button>

          <Button 
            onClick={() => window.history.back()} 
            variant="ghost" 
            size="lg" 
            className="w-full text-gray-600 hover:text-blue-900"
          >
            <ArrowLeft className="mr-2 h-5 w-5" />
            Go Back
          </Button>
        </div>

        {/* Help Text */}
        <div className="mt-8 p-4 bg-white rounded-lg shadow-sm">
          <p className="text-sm text-gray-600">
            Need help? <Link href="/contact" className="text-blue-900 hover:underline font-medium">Contact us</Link> or 
            call us at <a href="tel:+9779841234567" className="text-blue-900 hover:underline font-medium">+977-9841234567</a>
          </p>
        </div>
      </div>
    </div>
  )
}
