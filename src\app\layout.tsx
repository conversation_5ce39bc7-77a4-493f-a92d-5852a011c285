import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { QueryProvider } from "@/providers/query-provider";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { MusicAcademyStructuredData } from "@/components/seo/structured-data";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Sangam Music Academy - Learn Music with Passion | Biratnagar",
  description: "Premier music academy in Biratnagar offering guitar, piano, vocal, tabla, harmonium, and flute classes. Expert instructors, flexible schedules, and proven teaching methods.",
  keywords: "music academy, guitar classes, piano lessons, vocal training, tabla, harmonium, flute, Biratnagar, Nepal, music education",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <MusicAcademyStructuredData />
      </head>
      <body className={`${inter.variable} font-sans antialiased`}>
        <QueryProvider>
          <div className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
          </div>
        </QueryProvider>
      </body>
    </html>
  );
}
