'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { fetchCourses } from '@/lib/mock-data'
import { Clock, Users, DollarSign, Calendar, Filter } from 'lucide-react'

export default function CoursesPage() {
  const [selectedLevel, setSelectedLevel] = useState<string>('All')
  const [selectedInstrument, setSelectedInstrument] = useState<string>('All')

  const { data: courses, isLoading, error } = useQuery({
    queryKey: ['courses'],
    queryFn: fetchCourses,
  })

  if (isLoading) {
    return (
      <div className="min-h-screen">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-blue-900 to-blue-800 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Courses</h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Discover our comprehensive music programs designed for all skill levels
            </p>
          </div>
        </section>

        {/* Loading State */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-200 h-80 rounded-lg"></div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Courses</h2>
          <p className="text-gray-600">Please try again later.</p>
        </div>
      </div>
    )
  }

  // Filter courses based on selected filters
  const filteredCourses = courses?.filter(course => {
    const levelMatch = selectedLevel === 'All' || course.level === selectedLevel
    const instrumentMatch = selectedInstrument === 'All' || course.instrument === selectedInstrument
    return levelMatch && instrumentMatch
  }) || []

  // Get unique instruments and levels for filters
  const instruments = ['All', ...Array.from(new Set(courses?.map(course => course.instrument) || []))]
  const levels = ['All', ...Array.from(new Set(courses?.map(course => course.level) || []))]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Courses</h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Discover our comprehensive music programs designed for students of all ages and skill levels. 
            From beginner to advanced, we have the perfect course for your musical journey.
          </p>
        </div>
      </section>

      {/* Filters Section */}
      <section className="py-8 bg-gray-50 border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5 text-gray-600" />
              <span className="font-medium text-gray-900">Filter Courses:</span>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Level Filter */}
              <div className="flex gap-2">
                {levels.map((level) => (
                  <Button
                    key={level}
                    variant={selectedLevel === level ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedLevel(level)}
                    className={selectedLevel === level ? "bg-blue-900 hover:bg-blue-800" : ""}
                  >
                    {level}
                  </Button>
                ))}
              </div>

              {/* Instrument Filter */}
              <div className="flex gap-2 flex-wrap">
                {instruments.map((instrument) => (
                  <Button
                    key={instrument}
                    variant={selectedInstrument === instrument ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedInstrument(instrument)}
                    className={selectedInstrument === instrument ? "bg-blue-900 hover:bg-blue-800" : ""}
                  >
                    {instrument}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Courses Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {filteredCourses.length === 0 ? (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No courses found</h3>
              <p className="text-gray-600">Try adjusting your filters to see more courses.</p>
            </div>
          ) : (
            <>
              <div className="text-center mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  {filteredCourses.length} Course{filteredCourses.length !== 1 ? 's' : ''} Available
                </h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredCourses.map((course) => (
                  <Card key={course.id} className="card-hover border-0 shadow-lg overflow-hidden">
                    {/* Course Image */}
                    <div className="relative h-48">
                      <div
                        className="w-full h-full bg-cover bg-center"
                        style={{
                          backgroundImage: `url('${course.image}')`
                        }}
                      ></div>
                      <div className="absolute inset-0 bg-gradient-to-t from-blue-900/80 to-transparent"></div>
                      <div className="absolute bottom-4 left-4 right-4">
                        <div className="flex justify-between items-end">
                          <div>
                            <h3 className="text-xl font-bold text-white mb-1">{course.name}</h3>
                            <p className="text-blue-100 font-medium">{course.instrument}</p>
                          </div>
                          <Badge variant="secondary" className="bg-yellow-400 text-blue-900">
                            {course.level}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <CardContent className="p-6">
                      <p className="text-gray-600 mb-6 leading-relaxed">{course.description}</p>
                      
                      <div className="space-y-4 mb-6">
                        <div className="flex items-center text-sm text-gray-600">
                          <Users className="h-4 w-4 mr-3 text-blue-900" />
                          <span className="font-medium">Age Group:</span>
                          <span className="ml-2">{course.ageGroup}</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <Clock className="h-4 w-4 mr-3 text-blue-900" />
                          <span className="font-medium">Duration:</span>
                          <span className="ml-2">{course.duration}</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <Calendar className="h-4 w-4 mr-3 text-blue-900" />
                          <span className="font-medium">Schedule:</span>
                          <span className="ml-2">{course.schedule}</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <DollarSign className="h-4 w-4 mr-3 text-blue-900" />
                          <span className="font-medium">Fee:</span>
                          <span className="ml-2 text-lg font-bold text-blue-900">{course.fee}</span>
                        </div>
                      </div>

                      <Button asChild className="w-full bg-blue-900 hover:bg-blue-800">
                        <a href="/contact">
                          Enroll Now
                        </a>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Can&apos;t Find What You&apos;re Looking For?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            We offer customized lessons and can accommodate special requests. 
            Contact us to discuss your specific musical goals.
          </p>
          <Button asChild size="lg" className="bg-blue-900 hover:bg-blue-800">
            <a href="/contact">
              Contact Us for Custom Lessons
            </a>
          </Button>
        </div>
      </section>
    </div>
  )
}
